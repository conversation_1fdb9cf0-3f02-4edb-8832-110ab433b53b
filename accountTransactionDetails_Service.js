/**
 * Service layer for transactionCreate-related data operations
 * @description Service layer for 新增交易所需之(交易紀錄表、員工表、交易對象表等)相關資料操作 (服務層)
 * @module transactionCreate_Service
 */

/**
 * @description 獲取所有「帳戶」資料
 * @returns {Promise<Array>} 「帳戶」資料陣列
 */
async function getAccountsAll() {
    return await getAllData(STORE_NAMES.accounts);
}

/**
 * @description 根據 ID 獲取「帳戶」單一資料
 * @param {string} Id -  帳戶ID
 * @returns {Promise<Object>} 「帳戶」資料物件
 */
async function getAccountById(Id) {
    // This function assumes you have a function to get a single document by ID
    // You might need to implement this in your db.js
    return await getDataById(STORE_NAMES.accounts, Id);
}

/**
 * @description 根據 ID 獲取「帳戶類型」單一資料
 * @param {string} Id -  帳戶類型ID
 * @returns {Promise<Object>} 「帳戶類型」資料物件
 */
async function getAccountTypeById(Id) {
    // This function assumes you have a function to get a single document by ID
    // You might need to implement this in your db.js
    return await getDataById(STORE_NAMES.accountTypes, Id);
}

/**
 * @description 獲取所有「交易對象」資料
 * @returns {Promise<Array>} 「交易對象」資料陣列
 */
async function getEntitiesAll() {
    return await getAllData(STORE_NAMES.entities);
}

/**
 * @description 根據 ID 獲取「交易對象」單一資料
 * @param {string} Id -  交易對象ID
 * @returns {Promise<Object>} 「交易對象」資料物件
 */
async function getEntityById(Id) {
    // This function assumes you have a function to get a single document by ID
    // You might need to implement this in your db.js
    return await getDataById(STORE_NAMES.entities, Id);
}

/**
 * @description 獲取所有「員工」資料
 * @returns {Promise<Array>} 「員工」資料陣列
 */
async function getEmployeesAll() {
    return await getAllData(STORE_NAMES.employees);
}

/**
 * @description 根據 ID 獲取「員工」單一資料
 * @param {string} Id -  員工ID
 * @returns {Promise<Object>} 「員工」資料物件
 */

async function getEmployeeById(Id) {
    // This function assumes you have a function to get a single document by ID
    // You might need to implement this in your db.js
    return await getDataById(STORE_NAMES.employees, Id);
}

/**
 * @description 獲取所有「稅別」資料
 * @returns {Promise<Array>} 「稅別」資料陣列
 */
async function getTaxTypesRatesAll() {
    return await getAllData(STORE_NAMES.taxTypesRates);
}

/**
 * @description 獲取所有「交易項目分類」資料
 * @returns {Promise<Array>} 「交易項目分類」資料陣列
 */
async function getTransactionCategoriesAll() {
    return await getAllData(STORE_NAMES.transactionCategories);
}

/**
 * @description 獲取所有「交易項目分類之子項目」資料
 * @returns {Promise<Array>} 「交易項目分類之子項目」資料陣列
 */
async function getTransactionCategoryItemsAll() {
    return await getAllData(STORE_NAMES.transactionCategoryItems);
}

/**
 * @description 獲取所有「會計科目」資料
 * @returns {Promise<Array>} 「會計科目」資料陣列
 */
async function getAccountingItemsAll() {
    return await getAllData(STORE_NAMES.accountingItems);
}

/**
 * @description 根據 ID 獲取「交易紀錄」單一資料
 * @param {string} Id -  交易紀錄ID
 * @returns {Promise<Object>} 「交易紀錄」資料物件
 */
async function getTransactionById(Id) {
    // This function assumes you have a function to get a single document by ID
    // You might need to implement this in your db.js
    return await getDataById(STORE_NAMES.transactions, Id);
}

/**
 * @description 根據 ID 獲取「交易明細」單一資料
 * @param {string} Id -   交易紀錄ID (而非交易明細ID)
 * @returns {Promise<Object>} 「交易明細」資料物件
 */
async function getTransactionDetailByTransactionId(Id) {
    // This function assumes you have a function to get a single document by ID
    // You might need to implement this in your db.js
    return await getDataByIndex(STORE_NAMES.transactionDetails, 'transactionId', Id);
}


/**
 * @description 根據 ID 獲取「代墊款」單一資料
 * @param {string} Id -  代墊款ID
 * @returns {Promise<Object>} 「代墊款」資料物件
 */
async function getAdvancePaymentById(Id) {
    // This function assumes you have a function to get a single document by ID
    // You might need to implement this in your db.js
    return await getDataById(STORE_NAMES.advances, Id);
}

/**--------------------映射資料處理--------------------**/

// 輔助函數：根據帳戶ID獲取帳戶名稱
async function getAccountNameById(accountId) {
    try {
        if (!accountId) return '';
        const account = await getDataById(STORE_NAMES.accounts, accountId);
        return account ? account.name : '';
    } catch (error) {
        console.error('獲取帳戶名稱失敗:', error);
        return '';
    }
}

// 輔助函數：根據「帳戶ID」獲取 該帳戶之「帳戶類型」
// 解釋：此函數需要把 「帳戶」、「帳戶類型」 二個類型資料整合成一個查詢映射
async function getAccountTypeById(accountId) {
    try {
        if (!accountId) return '';
        const account = await getDataById(STORE_NAMES.accounts, accountId);
        if (!account) return '';
        const accountType = await getDataById(STORE_NAMES.accountTypes, account.accountTypeId);
        const accountInfo = {
            name: account.name,
            type: accountType.name
        }
        return accountInfo ? accountInfo : '';
    } catch (error) {
        console.error('獲取帳戶類型失敗:', error);
        return '';
    }
}

// 輔助函數：根據「帳戶ID」獲取 該帳戶之「帳戶名稱」和「帳戶類型」//////////////////////////<-- 這是舊版本 需進行整合處理
async function getAccount_NameType(accountID) {
    const account = await getDataById(STORE_NAMES.accounts, accountID);
    if (account) {
        const accountType = await getDataById(STORE_NAMES.accountTypes, account.accountTypeId);
        const accountInfo = {
            name: account.name,
            type: accountType.name
        }
        return accountInfo;
    } else {
        const accountInfo = {
            name: '未選擇',
            type: '未選擇'
        }
        return accountInfo;
    }
}

// 輔助函數：根據「交易對象ID」 和 「對象類型」獲取 「交易對象名稱」
// 解釋：此函數需要把 「員工」、「客戶」、「供應商」、「臨時交易對象」 四個類型資料整合成一個查詢映射
async function getEntityNameById(entityId, entityType) {
    try {
        let entityInfo ={};
        if (!entityId) return '';
        let entity;
        switch (entityType) {
            case 'employee':
                entity = await getDataById(STORE_NAMES.employees, entityId);
                break;
            case 'client':
            case 'supplier':
            case 'temporary':
                entity = await getDataById(STORE_NAMES.entities, entityId);
                if (entity) {
                    entityInfo = {
                        name: entity.name,
                        type: entity.type
                    }
                } else {
                    entityInfo = {
                        name: '未選擇',
                        type: '未選擇'
                    }
                }
                break;
            default:
                return '';
        }
        return entityInfo.name ? entityInfo : '';
    } catch (error) {
        console.error('獲取實體名稱失敗:', error);
        return '';
    }
}

// 輔助函數：根據「交易對象ID」 和 「對象類型」獲取 「交易對象名稱」和「交易對象類型」//////////////////////////<-- 這是舊版本 需進行整合處理
async function getEntity_NameType(entityID) {
    const entity = await getDataById(STORE_NAMES.entities, entityID);
    if (entity) {
        const entityInfo = {
            name: entity.name,
            type: entity.type
        }
        return entityInfo;
    } else {
        const entityInfo = {
            name: '未選擇',
            type: '未選擇'
        }
        return entityInfo;
    }
}

// 輔助函數：根據 「交易項目代碼Code」 獲取 「會計科目名稱」
async function getPaymentDescriptionName(paymentDescriptionCode) {
    try {
        if (!paymentDescriptionCode) return '';

        // 先從交易分類項目中查找
        const items = await getAllData(STORE_NAMES.transactionCategoryItems);
        const item = items.find(item => String(item.accountingCode) === String(paymentDescriptionCode));
        if (item) {
            return item.accountingName;
        }

        // 如果找不到，從會計項目中查找
        const accountingItems = await getAllData(STORE_NAMES.accountingItems);
        const accountingItem = accountingItems.find(item => String(item.code) === String(paymentDescriptionCode));
        return accountingItem ? accountingItem.name : '';
    } catch (error) {
        console.error('獲取交易項目名稱失敗:', error);
        return '';
    }
}

/**--------------------儲存或更新資料的處理--------------------**/

/**
 * @description 儲存交易資料 (新增或更新)，已是否傳入ID判斷
 * @param {} transactionId - 新增交易的交易編號
 */
async function saveTransactionToDB(transactionData) {//未實作完成
    if (transactionData.id) {
        // 如果有ID，則為編輯模式
        await updateData(STORE_NAMES.transactions, transactionData);
        return transactionData.id;
    } else {
        // 如果沒有ID，則為新增模式
        return await addData(STORE_NAMES.transactions, transactionData);
    }
}

/**
 * @description 儲存交易明細資料
 * @param {} transactionDetail - 新增交易明細
 */
async function saveTransactionDetailToDB(details) {
    return await addMultipleData(STORE_NAMES.transactionDetails, details);
}

/**
 * @description 更新交易明細資料
 * @param {} transactionDetail - 新增交易明細
 */
async function updateTransactionDetailToDB(details) {
    return await updateRelationData(STORE_NAMES.transactionDetails,'transactionId',details);
}

/**
 * @description 刪除交易明細資料
 * @param {} transactionId - 交易編號
 */
async function deleteTransactionDetailToDB(transactionId) {
    return await deleteDataByIndex(STORE_NAMES.transactionDetails,'transactionId',transactionId);
}

/**
 * @description 儲存會計分錄資料 (新增或更新)，已是否傳入ID判斷
 * @param {} journalId - 新增會計分錄的交易編號
 */
async function saveJournalEntriesToDB(transactionId, journalData) {
    return await saveJournalEntries(transactionId, journalData);
}

/**
 * @description 刪除會計分錄資料
 * @param {} transactionId - 交易編號
 */
async function deleteJournalEntriesToDB(transactionId) {
    return await deleteDataByIndex(STORE_NAMES.journalEntries, 'transactionId', transactionId);
}

/**
 * @description 儲存交易標籤資料 (新增或更新)，已是否傳入ID判斷
 * @param {} transactionTagId - 新增交易標籤的交易編號
 */

/**
 * @description 新增臨時交易對象
 * @param {}  - 新增臨時交易對象的資料
 */
async function addTemporaryEntity(temporaryEntity) {
    const entityData = {
        ...temporaryEntity,
        createdAt: new Date().toISOString(),
        type: temporaryEntity.type || 'temporary'
    };
    return await addData(STORE_NAMES.entities, entityData);
}

/**
 * @description 更新代墊款資料
 * @param {} advanceData - 代墊款資料
 */
async function updateAdvancePaymentToDB(advanceData) {
    return await updateData(STORE_NAMES.advances, advanceData);
}

/**
 * @description 更新薪資資料
 * @param {} salaryData - 薪資資料
 */
async function updateSalaryPaymentToDB(salaryData) {
    return await updateData(STORE_NAMES.salaries, salaryData);
}
