# 響應式分頁功能說明

## 📋 功能概述

實作了根據螢幕大小動態調整頁碼顯示數量的響應式分頁功能，確保在不同設備上都能提供最佳的用戶體驗。

## 📱 響應式設計規則

### 螢幕尺寸分級
- **手機版** (`< 480px`)：最多顯示 **3個頁碼**
- **平板版** (`480px - 768px`)：最多顯示 **5個頁碼**  
- **桌面版** (`> 768px`)：最多顯示 **7個頁碼**

### 動態調整邏輯
```javascript
function getMaxPageNumbers() {
    const screenWidth = window.innerWidth;
    
    if (screenWidth < 480) {
        return 3;  // 手機版
    } else if (screenWidth < 768) {
        return 5;  // 平板版
    } else {
        return 7;  // 桌面版
    }
}
```

## 🎯 不同螢幕的顯示效果

### 手機版 (< 480px)
```
顯示第 1 到 10 項，共 156 項結果
第 1 頁，共 16 頁

[<<] [<] [1] [2] [3] [>] [>>]

跳轉至
[___] 頁
[跳轉]
```

### 平板版 (480px - 768px)
```
顯示第 1 到 10 項，共 156 項結果        第 1 頁，共 16 頁

[<<] [<] [1] [2] [3] [4] [5] [>] [>>]

跳轉至 [___] 頁 [跳轉]
```

### 桌面版 (> 768px)
```
顯示第 1 到 10 項，共 156 項結果                    第 1 頁，共 16 頁

[<<] [<] [1] [2] [3] [4] [5] ... [16] [>] [>>]

跳轉至 [___] 頁 [跳轉]
```

## 🔧 技術實現

### 1. 動態頁碼計算
```javascript
// 根據螢幕大小動態調整顯示的頁碼數量
const maxPageNumbers = getMaxPageNumbers();
const sidePages = Math.floor((maxPageNumbers - 1) / 2); // 當前頁兩側顯示的頁數

// 計算顯示的頁碼範圍
let startPage = Math.max(1, currentPage - sidePages);
let endPage = Math.min(totalPages, currentPage + sidePages);
```

### 2. 窗口大小監聽
```javascript
// 監聽窗口大小變化，動態調整分頁顯示
window.addEventListener('resize', debounce(() => {
    if (dataPaging && dataPaging.getTotalPages() > 1) {
        generatePageNumbers();
    }
}, 250));
```

### 3. 防抖優化
```javascript
// 防抖函數，避免頻繁重新渲染
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
```

## 🎨 響應式樣式

### CSS 類別設計
```html
<!-- 按鈕間距：小螢幕較緊密，大螢幕較寬鬆 -->
<div class="space-x-0.5 sm:space-x-1">

<!-- 按鈕大小：小螢幕較小的 padding 和字體 -->
<button class="px-2 sm:px-3 py-2 text-xs sm:text-sm">

<!-- 佈局方向：小螢幕垂直排列，大螢幕水平排列 -->
<div class="flex flex-col sm:flex-row">
```

### 具體樣式調整

#### 分頁資訊區域
- **手機版**：垂直排列，居中對齊
- **桌面版**：水平排列，兩端對齊

#### 分頁按鈕區域
- **手機版**：較小的 padding (`px-2`) 和字體 (`text-xs`)
- **桌面版**：正常的 padding (`px-3`) 和字體 (`text-sm`)

#### 快速跳轉區域
- **手機版**：垂直排列，元素間有垂直間距
- **桌面版**：水平排列，元素間有水平間距

## 📊 頁碼顯示範例

### 手機版 (3個頁碼)
```
總頁數 = 20，當前頁 = 10
顯示：[1] [...] [10] [...] [20]

總頁數 = 20，當前頁 = 2
顯示：[1] [2] [3]

總頁數 = 20，當前頁 = 19
顯示：[18] [19] [20]
```

### 平板版 (5個頁碼)
```
總頁數 = 20，當前頁 = 10
顯示：[1] [...] [9] [10] [11] [...] [20]

總頁數 = 20，當前頁 = 3
顯示：[1] [2] [3] [4] [5]

總頁數 = 20，當前頁 = 18
顯示：[16] [17] [18] [19] [20]
```

### 桌面版 (7個頁碼)
```
總頁數 = 20，當前頁 = 10
顯示：[1] [...] [7] [8] [9] [10] [11] [12] [13] [...] [20]

總頁數 = 20，當前頁 = 4
顯示：[1] [2] [3] [4] [5] [6] [7] [...] [20]

總頁數 = 20，當前頁 = 17
顯示：[1] [...] [14] [15] [16] [17] [18] [19] [20]
```

## 🚀 性能優化

### 1. 防抖處理
- 窗口大小變化時使用 250ms 防抖
- 避免頻繁重新渲染分頁按鈕

### 2. 條件渲染
- 只在總頁數 > 1 時才重新生成頁碼
- 避免不必要的 DOM 操作

### 3. 事件優化
- 使用 `onclick` 屬性而非事件監聽器
- 減少記憶體使用和事件管理複雜度

## 📱 用戶體驗優化

### 觸控友好
- **較大的點擊區域**：確保手指點擊的準確性
- **適當的間距**：避免誤觸相鄰按鈕

### 視覺清晰
- **響應式字體**：小螢幕使用較小字體保持清晰
- **適應性佈局**：根據螢幕方向調整排列方式

### 操作便利
- **保留核心功能**：所有螢幕尺寸都保留完整的分頁功能
- **智能省略**：在有限空間內顯示最重要的頁碼

## 🔄 動態更新機制

### 觸發條件
1. **窗口大小變化**：用戶調整瀏覽器窗口
2. **設備旋轉**：手機/平板橫豎屏切換
3. **頁面載入**：初始載入時根據當前螢幕大小設置

### 更新流程
```
窗口大小變化 → 防抖處理 → 重新計算最大頁碼數 → 重新生成頁碼按鈕 → 更新 DOM
```

## 🧪 測試建議

### 響應式測試
1. **瀏覽器開發者工具**：模擬不同設備尺寸
2. **實際設備測試**：在真實的手機、平板上測試
3. **窗口調整測試**：手動調整瀏覽器窗口大小

### 功能測試
1. **頁碼點擊**：確保所有尺寸下頁碼都可點擊
2. **快速跳轉**：驗證跳轉功能在各尺寸下正常
3. **狀態同步**：確保響應式變化不影響當前頁狀態

## 🔮 未來擴展

### 可能的改進
1. **更細緻的斷點**：增加更多螢幕尺寸斷點
2. **用戶偏好**：允許用戶自定義頁碼顯示數量
3. **手勢支援**：在觸控設備上支援滑動翻頁
4. **無障礙優化**：改善螢幕閱讀器的支援

### 技術優化
1. **CSS Container Queries**：使用更現代的響應式技術
2. **Intersection Observer**：優化可見性檢測
3. **Web Components**：封裝為可重用組件
