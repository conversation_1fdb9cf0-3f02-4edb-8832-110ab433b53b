// 分頁設定
const PAGE_SIZE = 10;//每頁顯示10筆交易
let dataPaging;

let filteredTransactions = [];
let futureFilteredTransactions = [];
let currentAccountId = null;
let currentAccountBalance = 0;

// 標籤過濾相關變數
let selectedTags = []; // 存儲選中的標籤
let tagFilterMode = 'OR'; // 標籤過濾模式：'OR' 或 'AND'
let allAvailableTags = []; // 存儲所有可用的標籤

//--------------------------------工具函數--------------------------------

// 防抖函數
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}


// 初始化頁面
document.addEventListener('DOMContentLoaded', async () => {
    try {        
        // 載入帳戶列表
        await loadAccountsSelector();
        
        // 從 URL 參數獲取選定的帳戶資訊
        const accountId = getAccountIdFromUrl();
        if(accountId){
            setAccountSelector(accountId);
        }else{
            setAccountSelector("");
        }
        
        
        // 綁定篩選表單事件
        document.getElementById('filterForm').addEventListener('submit', handleFilter);
        
        // 綁定分頁按鈕事件
        document.getElementById('firstPage').addEventListener('click', () => goToPage(1));
        document.getElementById('prevPage').addEventListener('click', () => changePage(-1));
        document.getElementById('nextPage').addEventListener('click', () => changePage(1));
        document.getElementById('lastPage').addEventListener('click', () => goToLastPage());
        document.getElementById('jumpButton').addEventListener('click', handleJumpToPage);

        // 綁定快速跳轉輸入框事件
        document.getElementById('jumpToPage').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                handleJumpToPage();
            }
        });

        // 初始化標籤過濾功能
        initializeTagFiltering();

        // 初始化標籤搜尋功能
        initializeTagSearch();

        // 監聽窗口大小變化，動態調整分頁顯示
        window.addEventListener('resize', debounce(() => {
            if (dataPaging && dataPaging.getTotalPages() > 1) {
                generatePageNumbers();
            }
        }, 250));

    } catch (error) {
        console.error('載入資料失敗：', error);
        alert('載入資料失敗，請重新整理頁面');
    }
});
//--------------------------------URL參數處理--------------------------------
// 從 URL 參數獲取選定的帳戶資訊
function getAccountIdFromUrl() {
    const urlParams = new URLSearchParams(window.location.search);
    const accountId = urlParams.get('accountId');
    if(accountId){
        return accountId;
    }else{
        return null;
    }
}

//--------------------------------分頁操作--------------------------------
// 切換頁面
function changePage(delta) {
    const newPage = dataPaging.getCurrentPage() + delta;
    if (newPage >= 1 && newPage <= dataPaging.getTotalPages()) {
        dataPaging.getDataOfPage(newPage);
        updateDisplay();
    }
}

// 跳轉到指定頁面
function goToPage(pageNumber) {
    if (pageNumber >= 1 && pageNumber <= dataPaging.getTotalPages()) {
        dataPaging.getDataOfPage(pageNumber);
        updateDisplay();
    }
}

// 跳轉到最後一頁
function goToLastPage() {
    const lastPage = dataPaging.getTotalPages();
    if (lastPage > 0) {
        dataPaging.getDataOfPage(lastPage);
        updateDisplay();
    }
}

// 處理快速跳轉
function handleJumpToPage() {
    const jumpInput = document.getElementById('jumpToPage');
    const pageNumber = parseInt(jumpInput.value);

    if (isNaN(pageNumber)) {
        alert('請輸入有效的頁碼');
        return;
    }

    if (pageNumber < 1 || pageNumber > dataPaging.getTotalPages()) {
        alert(`頁碼必須在 1 到 ${dataPaging.getTotalPages()} 之間`);
        return;
    }

    goToPage(pageNumber);
    jumpInput.value = ''; // 清空輸入框
}

// 根據螢幕大小獲取最大頁碼顯示數量
function getMaxPageNumbers() {
    const screenWidth = window.innerWidth;

    if (screenWidth < 480) {
        // 手機版：最多顯示3個頁碼
        return 3;
    } else if (screenWidth < 768) {
        // 平板版：最多顯示5個頁碼
        return 5;
    } else {
        // 桌面版：最多顯示7個頁碼
        return 7;
    }
}

// 生成頁碼按鈕
function generatePageNumbers() {
    const container = document.getElementById('pageNumbersContainer');
    const currentPage = dataPaging.getCurrentPage();
    const totalPages = dataPaging.getTotalPages();

    container.innerHTML = '';

    if (totalPages <= 1) return;

    // 根據螢幕大小動態調整顯示的頁碼數量
    const maxPageNumbers = getMaxPageNumbers();
    const sidePages = Math.floor((maxPageNumbers - 1) / 2); // 當前頁兩側顯示的頁數

    // 計算顯示的頁碼範圍
    let startPage = Math.max(1, currentPage - sidePages);
    let endPage = Math.min(totalPages, currentPage + sidePages);

    // 確保顯示足夠的頁碼（如果總頁數足夠）
    const actualPageNumbers = endPage - startPage + 1;
    if (actualPageNumbers < maxPageNumbers && totalPages >= maxPageNumbers) {
        if (startPage === 1) {
            endPage = Math.min(totalPages, startPage + maxPageNumbers - 1);
        } else if (endPage === totalPages) {
            startPage = Math.max(1, endPage - maxPageNumbers + 1);
        }
    }

    // 如果起始頁不是第1頁，顯示省略號
    if (startPage > 1) {
        if (startPage > 2) {
            // 添加第1頁
            const firstPageBtn = createPageButton(1, false);
            container.appendChild(firstPageBtn);

            // 添加省略號
            const ellipsis = document.createElement('span');
            ellipsis.className = 'px-3 py-2 text-sm text-gray-500';
            ellipsis.textContent = '...';
            container.appendChild(ellipsis);
        }
    }

    // 生成頁碼按鈕
    for (let i = startPage; i <= endPage; i++) {
        const pageBtn = createPageButton(i, i === currentPage);
        container.appendChild(pageBtn);
    }

    // 如果結束頁不是最後一頁，顯示省略號
    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            // 添加省略號
            const ellipsis = document.createElement('span');
            ellipsis.className = 'px-3 py-2 text-sm text-gray-500';
            ellipsis.textContent = '...';
            container.appendChild(ellipsis);

            // 添加最後一頁
            const lastPageBtn = createPageButton(totalPages, false);
            container.appendChild(lastPageBtn);
        }
    }
}

// 創建頁碼按鈕
function createPageButton(pageNumber, isActive) {
    const button = document.createElement('button');
    button.textContent = pageNumber;
    button.onclick = () => goToPage(pageNumber);

    // 響應式樣式：小螢幕使用較小的 padding 和字體
    const baseClasses = 'px-2 sm:px-3 py-2 text-xs sm:text-sm font-medium border-t border-b border-gray-300';

    if (isActive) {
        button.className = `${baseClasses} text-white bg-blue-600 border-blue-600 hover:bg-blue-700`;
    } else {
        button.className = `${baseClasses} text-gray-500 bg-white hover:bg-gray-50`;
    }

    return button;
}

//更新頁面資訊
function updatePageInfo() {
    if (!dataPaging) return;

    const currentPage = dataPaging.getCurrentPage();
    const totalPages = dataPaging.getTotalPages();

    // 嘗試獲取總項目數和頁面大小，如果方法不存在則使用備用計算
    let totalItems, pageSize;
    try {
        totalItems = dataPaging.getTotalItems ? dataPaging.getTotalItems() : futureFilteredTransactions.length;
        pageSize = dataPaging.getPageSize ? dataPaging.getPageSize() : PAGE_SIZE;
    } catch (error) {
        totalItems = futureFilteredTransactions.length;
        pageSize = PAGE_SIZE;
    }

    // 計算當前頁顯示的項目範圍
    const startItem = (currentPage - 1) * pageSize + 1;
    const endItem = Math.min(currentPage * pageSize, totalItems);

    // 更新分頁資訊
    document.getElementById('pageStartItem').textContent = totalItems > 0 ? startItem : 0;
    document.getElementById('pageEndItem').textContent = endItem;
    document.getElementById('totalItems').textContent = totalItems;
    document.getElementById('currentPageNum').textContent = currentPage;
    document.getElementById('totalPages').textContent = totalPages;

    // 更新按鈕狀態
    document.getElementById('firstPage').disabled = currentPage === 1;
    document.getElementById('prevPage').disabled = currentPage === 1;
    document.getElementById('nextPage').disabled = currentPage === totalPages || totalPages === 0;
    document.getElementById('lastPage').disabled = currentPage === totalPages || totalPages === 0;

    // 更新快速跳轉輸入框的最大值
    const jumpInput = document.getElementById('jumpToPage');
    jumpInput.max = totalPages;
    jumpInput.placeholder = totalPages > 0 ? `1-${totalPages}` : '頁';

    // 生成頁碼按鈕
    generatePageNumbers();
}



//--------------------------------UI元件事件處理--------------------------------

// 處理篩選表單提交
async function handleFilter(event) {
    event.preventDefault();

    const filters = {
        type: document.getElementById('typeFilter').value,
        startDate: document.getElementById('startDate').value,
        endDate: document.getElementById('endDate').value,
        tags: selectedTags // 添加標籤篩選條件
    };

    await loadTransactions(filters);
}

//--------------------------------標籤過濾功能--------------------------------

// 處理標籤點擊事件
function handleTagClick(tagName, event) {
    event.stopPropagation(); // 防止事件冒泡

    const tagIndex = selectedTags.indexOf(tagName);

    if (tagIndex === -1) {
        // 標籤未選中，添加到選中列表
        selectedTags.push(tagName);
    } else {
        // 標籤已選中，從選中列表移除
        selectedTags.splice(tagIndex, 1);
    }

    // 保存標籤狀態到 URL
    saveTagsToUrl();

    // 更新標籤顯示狀態
    updateTagDisplayStates();

    // 重新載入交易記錄以應用過濾
    handleFilter(event);
}

// 更新標籤顯示狀態
function updateTagDisplayStates() {
    const allTagElements = document.querySelectorAll('[data-tag-name]');

    allTagElements.forEach(tagElement => {
        const tagName = tagElement.getAttribute('data-tag-name');
        const isSelected = selectedTags.includes(tagName);

        if (isSelected) {
            // 選中狀態：藍色背景
            tagElement.className = 'px-2 ml-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800 cursor-pointer hover:bg-blue-200 transition-colors';
        } else {
            // 未選中狀態：原始樣式
            tagElement.className = 'px-2 ml-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-50 text-red-800 cursor-pointer hover:bg-red-100 transition-colors';
        }
    });

    // 更新篩選器區域的標籤顯示
    updateSelectedTagsDisplay();
}

// 清除單個標籤過濾
function removeTagFilter(tagName) {
    const tagIndex = selectedTags.indexOf(tagName);
    if (tagIndex !== -1) {
        selectedTags.splice(tagIndex, 1);
        saveTagsToUrl();
        updateTagDisplayStates();

        // 重新載入交易記錄
        const event = new Event('submit');
        handleFilter(event);
    }
}

// 清除所有標籤過濾
function clearAllTagFilters() {
    selectedTags = [];
    saveTagsToUrl();
    updateTagDisplayStates();

    // 重新載入交易記錄
    const event = new Event('submit');
    handleFilter(event);
}

// 更新已選擇標籤的顯示區域
function updateSelectedTagsDisplay() {
    const selectedTagsContainer = document.getElementById('selectedTagsContainer');
    if (!selectedTagsContainer) return;

    if (selectedTags.length === 0) {
        selectedTagsContainer.style.display = 'none';
        return;
    }

    selectedTagsContainer.style.display = 'block';
    const tagsHtml = selectedTags.map(tag =>
        `<span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 mr-2 mb-2">
            ${tag}
            <button type="button" onclick="removeTagFilter('${tag}')" class="ml-1 text-blue-600 hover:text-blue-800">
                <i class="fas fa-times"></i>
            </button>
        </span>`
    ).join('');

    selectedTagsContainer.innerHTML = `
        <div class="flex items-center justify-between flex-wrap">
            <div class="flex items-center flex-wrap">
                <span class="text-sm font-medium text-gray-700 mr-2">已選擇標籤：</span>
                ${tagsHtml}
                <button type="button" onclick="clearAllTagFilters()" class="text-sm text-red-600 hover:text-red-800 ml-2">
                    清除全部
                </button>
            </div>
            <div class="text-sm text-gray-500 mt-2 sm:mt-0">
                <i class="fas fa-filter mr-1"></i>
                過濾模式：${tagFilterMode === 'AND' ? '包含所有標籤' : '包含任一標籤'}
                <button type="button" onclick="toggleTagFilterMode()" class="ml-2 text-blue-600 hover:text-blue-800">
                    切換
                </button>
            </div>
        </div>
    `;
}

// 切換標籤過濾模式
function toggleTagFilterMode() {
    tagFilterMode = tagFilterMode === 'AND' ? 'OR' : 'AND';
    updateSelectedTagsDisplay();

    // 如果有選中的標籤，重新應用過濾
    if (selectedTags.length > 0) {
        const event = new Event('submit');
        handleFilter(event);
    }
}

// 初始化標籤過濾功能
function initializeTagFiltering() {
    // 從 URL 參數中恢復選中的標籤（如果有的話）
    const urlParams = new URLSearchParams(window.location.search);
    const tagsParam = urlParams.get('tags');
    if (tagsParam) {
        selectedTags = tagsParam.split(',').filter(tag => tag.trim() !== '');
    }

    // 初始化已選擇標籤的顯示
    updateSelectedTagsDisplay();
}

// 將選中的標籤保存到 URL 參數中（可選功能）
function saveTagsToUrl() {
    const url = new URL(window.location);
    if (selectedTags.length > 0) {
        url.searchParams.set('tags', selectedTags.join(','));
    } else {
        url.searchParams.delete('tags');
    }
    window.history.replaceState({}, '', url);
}

//--------------------------------標籤搜尋功能--------------------------------

// 初始化標籤搜尋功能
function initializeTagSearch() {
    const searchInput = document.getElementById('tagSearchInput');
    const dropdown = document.getElementById('tagSearchDropdown');

    if (!searchInput || !dropdown) return;

    // 綁定搜尋輸入事件
    searchInput.addEventListener('input', handleTagSearch);
    searchInput.addEventListener('focus', handleTagSearchFocus);
    searchInput.addEventListener('blur', handleTagSearchBlur);
    searchInput.addEventListener('keydown', handleTagSearchKeydown);

    // 點擊外部關閉下拉選單
    document.addEventListener('click', (event) => {
        if (!searchInput.contains(event.target) && !dropdown.contains(event.target)) {
            hideTagSearchDropdown();
        }
    });
}

// 載入所有可用標籤
async function loadAllAvailableTags() {
    try {
        if (!currentAccountId) {
            allAvailableTags = [];
            return;
        }

        // 從當前帳戶的所有交易中提取標籤
        const transactions = await getAllData('transactions');
        const accountTransactions = transactions.filter(t =>
            t.accountId === currentAccountId ||
            (t.entityId === currentAccountId && t.entityType === 'account')
        );

        // 提取所有標籤並去重
        const tagSet = new Set();
        accountTransactions.forEach(transaction => {
            if (transaction.tags && Array.isArray(transaction.tags)) {
                transaction.tags.forEach(tag => {
                    if (tag && tag.trim() !== '') {
                        tagSet.add(tag.trim());
                    }
                });
            }
        });

        allAvailableTags = Array.from(tagSet).sort();
    } catch (error) {
        console.error('載入可用標籤失敗：', error);
        allAvailableTags = [];
    }
}

// 處理標籤搜尋輸入
function handleTagSearch(event) {
    const searchTerm = event.target.value.trim();

    if (searchTerm === '') {
        hideTagSearchDropdown();
        return;
    }

    // 模糊搜尋標籤
    const filteredTags = allAvailableTags.filter(tag =>
        tag.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !selectedTags.includes(tag)
    );

    displayTagSearchResults(filteredTags, searchTerm);
}

// 處理搜尋框獲得焦點
function handleTagSearchFocus(event) {
    const searchTerm = event.target.value.trim();
    if (searchTerm !== '') {
        handleTagSearch(event);
    }
}

// 處理搜尋框失去焦點
function handleTagSearchBlur() {
    // 延遲隱藏，讓點擊事件能夠觸發
    setTimeout(() => {
        hideTagSearchDropdown();
    }, 200);
}

// 處理鍵盤導航
function handleTagSearchKeydown(event) {
    const dropdown = document.getElementById('tagSearchDropdown');
    if (dropdown.classList.contains('hidden')) return;

    const items = dropdown.querySelectorAll('[data-tag-index]');
    if (items.length === 0) return;

    let currentIndex = -1;
    const activeItem = dropdown.querySelector('.bg-blue-100');
    if (activeItem) {
        currentIndex = parseInt(activeItem.getAttribute('data-tag-index'));
    }

    switch (event.key) {
        case 'ArrowDown':
            event.preventDefault();
            currentIndex = Math.min(currentIndex + 1, items.length - 1);
            highlightSearchItem(items, currentIndex);
            break;

        case 'ArrowUp':
            event.preventDefault();
            currentIndex = Math.max(currentIndex - 1, 0);
            highlightSearchItem(items, currentIndex);
            break;

        case 'Enter':
            event.preventDefault();
            if (currentIndex >= 0 && items[currentIndex]) {
                const tagName = items[currentIndex].textContent.trim();
                selectTagFromSearch(tagName);
            }
            break;

        case 'Escape':
            event.preventDefault();
            hideTagSearchDropdown();
            break;
    }
}

// 高亮搜尋項目
function highlightSearchItem(items, index) {
    // 移除所有高亮
    items.forEach(item => {
        item.classList.remove('bg-blue-100');
    });

    // 高亮當前項目
    if (index >= 0 && index < items.length) {
        items[index].classList.add('bg-blue-100');
        items[index].scrollIntoView({ block: 'nearest' });
    }
}

// 顯示標籤搜尋結果
function displayTagSearchResults(tags, searchTerm) {
    const dropdown = document.getElementById('tagSearchDropdown');

    if (tags.length === 0) {
        dropdown.innerHTML = `
            <div class="px-4 py-3 text-gray-500 text-sm text-center">
                <i class="fas fa-search text-gray-300 mb-1"></i>
                <div>沒有找到匹配的標籤</div>
            </div>
        `;
    } else {
        dropdown.innerHTML = tags.map((tag, index) => {
            // 高亮搜尋關鍵字
            const highlightedTag = highlightSearchTerm(tag, searchTerm);
            return `
                <div class="px-4 py-3 hover:bg-blue-50 cursor-pointer border-b border-gray-100 last:border-b-0 transition-colors duration-150"
                     onclick="selectTagFromSearch('${tag}')"
                     data-tag-index="${index}">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-800">${highlightedTag}</span>
                        <i class="fas fa-plus text-gray-400 text-xs"></i>
                    </div>
                </div>
            `;
        }).join('');
    }

    dropdown.classList.remove('hidden');
}

// 高亮搜尋關鍵字
function highlightSearchTerm(text, searchTerm) {
    if (!searchTerm) return text;

    const regex = new RegExp(`(${searchTerm})`, 'gi');
    return text.replace(regex, '<mark class="bg-yellow-200">$1</mark>');
}

// 從搜尋結果中選擇標籤
function selectTagFromSearch(tagName) {
    // 清理標籤名稱（移除HTML標記）
    const cleanTagName = tagName.replace(/<[^>]*>/g, '').trim();

    // 添加到選中標籤列表
    if (!selectedTags.includes(cleanTagName)) {
        selectedTags.push(cleanTagName);

        // 保存狀態
        saveTagsToUrl();

        // 更新顯示
        updateTagDisplayStates();
        updateSelectedTagsDisplay();

        // 清空搜尋框
        const searchInput = document.getElementById('tagSearchInput');
        if (searchInput) {
            searchInput.value = '';
        }

        // 隱藏下拉選單
        hideTagSearchDropdown();

        // 重新載入交易記錄以應用過濾
        const event = new Event('submit');
        handleFilter(event);
    }
}

// 隱藏標籤搜尋下拉選單
function hideTagSearchDropdown() {
    const dropdown = document.getElementById('tagSearchDropdown');
    if (dropdown) {
        dropdown.classList.add('hidden');
    }
}


// 帳戶選擇器指定帳戶
async function setAccountSelector(accountId){
    const accountSelect = document.getElementById('accountSelect');
    accountSelect.value = accountId;
    handleAccountChange();
}

// 帳戶選擇變更時
async function handleAccountChange() {
    const accountId = document.getElementById('accountSelect').value;
    currentAccountId = accountId;
    if (accountId) {
        // 顯示餘額卡片
        document.getElementById('balanceCard').classList.remove('hidden');
    } else {
        // 隱藏餘額卡片
        document.getElementById('balanceCard').classList.add('hidden');
        // 隱藏過濾結果統計卡片
        const filteredBalanceCard = document.getElementById('filteredBalanceCard');
        if (filteredBalanceCard) {
            filteredBalanceCard.style.display = 'none';
        }
    }

    // 獲取當前的篩選條件
    const filters = {
        type: document.getElementById('typeFilter').value,
        startDate: document.getElementById('startDate').value,
        endDate: document.getElementById('endDate').value,
        tags: selectedTags
    };

    // 載入該帳戶的可用標籤
    await loadAllAvailableTags();

    await loadTransactions(filters);
}

//--------------------------------資料載入--------------------------------
// 載入帳戶選擇器
async function loadAccountsSelector() {
    try {
        const accounts = await getAllData('accounts');
        const accountSelect = document.getElementById('accountSelect');
        accountSelect.innerHTML = '<option value="">請選擇帳戶</option>';
        
        // 先載入銀行列表
        const banks = await getAllData('banks');
        const banksMap = new Map(banks.map(bank => [bank.code, bank]));
        
        // 載入帳戶類型列表（暫時未使用）
        // const accountTypes = await getAllData('accountTypes');
        // const accountTypesMap = new Map(accountTypes.map(type => [type.id, type]));



        accounts.forEach(account => {
            const bank = banksMap.get(account.bankCode);
            // const accountType = accountTypesMap.get(account.accountTypeId); // 暫時未使用
            const option = document.createElement('option');
            option.value = account.id;
            option.textContent = `${account.name} - ${bank ? bank.name : '未知銀行'} (${account.accountNumber.replace(/(\d{4})/g, '$1-').replace(/-$/, '')})`;
            accountSelect.appendChild(option);
        });
    } catch (error) {
        console.error('載入帳戶列表失敗：', error);
        alert('載入帳戶列表失敗');
    }
}

// 載入交易記錄
async function loadTransactions(filters = {}) {
    try {
        if (!currentAccountId) {
            // 隱藏過濾結果統計卡片
            const filteredBalanceCard = document.getElementById('filteredBalanceCard');
            if (filteredBalanceCard) {
                filteredBalanceCard.style.display = 'none';
            }
            await updateDisplay();
            return;
        }
        // 從資料庫獲取所有交易
        let transactions = await getAllData('transactions');
        let futureTransactions = await getAllData('transactions');



        // 篩選指定帳戶的交易（包含轉入轉出）
        const accountTransactions = transactions.filter(t =>
            (t.accountId === currentAccountId || t.entityId === currentAccountId && t.entityType === 'account')
            && (t.transactionStatus=== 'completed')  //必須為完成狀態
            && ((t.expectedPaymentDate=== null || t.expectedPaymentDate === '' || t.expectedPaymentDate === undefined || !t.expectedPaymentDate )
            || new Date(t.expectedPaymentDate) <= new Date() || t.paymentDate) //不能有預計付款日期 或是 以填入付款日期 或是 預計付款日期已到
        );

        //篩選指定帳戶的所有交易（包含未來）
        const accountFutureTransactions = futureTransactions.filter(t =>
            t.accountId === currentAccountId ||
            t.entityId === currentAccountId && t.entityType === 'account' 
        );



        // 應用篩選條件到交易記錄
        filteredTransactions = applyFilters(accountTransactions, filters);
        futureFilteredTransactions = applyFilters(accountFutureTransactions, filters);

        //獲取帳戶初始餘額
        const account = await getDataById(STORE_NAMES.accounts, currentAccountId);

        // 計算帳戶真實餘額（不受過濾影響）
        const realCurrentBalance = account.balance + await calculateAccountBalance(accountTransactions);
        const realFutureBalance = account.balance + await calculateAccountBalance(accountFutureTransactions);

        // 更新帳戶餘額顯示（不受過濾影響）
        updateBalanceDisplay(realCurrentBalance, realFutureBalance);

        // 計算過濾後的金額統計
        const filteredCurrentBalance = await calculateAccountBalance(filteredTransactions);
        const filteredFutureBalance = await calculateAccountBalance(futureFilteredTransactions);

        // 更新過濾結果統計顯示
        updateFilteredBalanceDisplay(filteredCurrentBalance, filteredFutureBalance, filters);
    

        //分頁前先排序，依照paymentDate排序，paymentDate為空則依照expectedPaymentDate排序
        futureFilteredTransactions.sort((a, b) => {
            const dateA = a.paymentDate || a.expectedPaymentDate;
            const dateB = b.paymentDate || b.expectedPaymentDate;
            return dateA ? new Date(dateA).getTime() - new Date(dateB).getTime() : new Date(dateB).getTime() - new Date(dateA).getTime();
        });

        //依照目前資料建立分頁器
        dataPaging = new DataPaging(futureFilteredTransactions, PAGE_SIZE);

        // 更新顯示
        await updateDisplay();
    } catch (error) {
        console.error('載入交易記錄失敗：', error);
        alert('載入交易記錄失敗');
    }
}

// 計算帳戶餘額
async function calculateAccountBalance(transactions) {
    let balance = 0;
    
    for (const transaction of transactions) {
        if (transaction.transactionType === 'income') {
            balance += transaction.amount;
        } else if (transaction.transactionType === 'expense') {
            balance -= transaction.amount;
        } else if (transaction.transactionType === 'transfer') {
            // 如果是轉帳，需要判斷是轉入還是轉出
            if (transaction.accountId === currentAccountId) {
                // 如果是從當前帳戶轉出
                balance -= transaction.amount;
            } else if (transaction.entityId === currentAccountId) {
                // 如果是轉入當前帳戶
                balance += transaction.amount;
            }
        }
    }
    
    return balance;
}

// 應用篩選條件
function applyFilters(transactions, filters) {
    return transactions.filter(transaction => {
        // 交易類型篩選
        if (filters.type && transaction.transactionType !== filters.type) {
            return false;
        }

        // 日期範圍篩選
        const dateToCheck = transaction.paymentDate || transaction.expectedPaymentDate;
        if (!dateToCheck) return true;

        if (filters.startDate) {
            const start = new Date(filters.startDate);
            const transactionDate = new Date(dateToCheck);
            if (transactionDate < start) return false;
        }

        if (filters.endDate) {
            const end = new Date(filters.endDate);
            const transactionDate = new Date(dateToCheck);
            if (transactionDate > end) return false;
        }

        // 標籤篩選
        if (filters.tags && filters.tags.length > 0) {
            const transactionTags = transaction.tags || [];

            if (tagFilterMode === 'AND') {
                // AND 模式：交易必須包含所有選中的標籤
                return filters.tags.every(tag => transactionTags.includes(tag));
            } else {
                // OR 模式：交易只需包含任一選中的標籤
                return filters.tags.some(tag => transactionTags.includes(tag));
            }
        }

        return true;
    });
}
//--------------------------------資料查詢--------------------------------

// 查詢帳戶名稱
async function getAccountName(accountId) {
    try {
        const account = await getDataById(STORE_NAMES.accounts, accountId);
        return account ? account.name : '未知帳戶';
    } catch (error) {
        console.error('查詢帳戶名稱時發生錯誤：', error);
        return '未知帳戶';
    }
}
// 查詢員工名稱
async function getEmployeeName(employeeId) {
    const employee = await getDataById(STORE_NAMES.employees, employeeId);
    return employee ? employee.name : '未知員工';
}

// 查詢對象名稱
async function getEntity(entityId) {
    try {
        let storeName = STORE_NAMES.entities;
        
        const entity = await getDataById(storeName, entityId);


        return entity ;
    } catch (error) {

        console.error('查詢對象名稱時發生錯誤：', error);
        return '未知對象';
    }
}

// 取得交易對象資訊
async function getTargetEntityInfo(transaction) {
    let entityInfo;
    try {
        // 獲取帳戶和對象名稱
        let entityName;
        let entityType;
        if(transaction.entityType === 'account'){
            entityName = await getAccountName(transaction.entityId);
            entityType = '帳戶';
        }else if(transaction.entityType === 'employee'){
            entityName = await getEmployeeName(transaction.entityId);
            entityType = '員工';
        }else{
            if(transaction.entityId){
                const entity = await getEntity(transaction.entityId);
                if(entity){
                    entityName = entity.name;
                    entityType = getEntityTypeLabel(entity.type);
                }else{
                    entityName = '可能被已刪除';
                    entityType = '對象';
                }
            }else{
                entityName = '無設定';
                entityType = '對象';
            }
        }
        entityInfo = {
            type: entityType,
            name: entityName
        };
        return entityInfo;
    } catch (error) {
        console.error('取得交易對象資訊失敗：', error);
        return null;
    }
}

// 取得交易描述的完整顯示文字
async function getTransactionDescription(accountingCode, transactionType) {
    if (!accountingCode) return '無描述';
    
    try {
        const categories = await getAllData('transactionCategories');
        const items = await getAllData('transactionCategoryItems');
        
        // 找到對應的會計項目
        const item = items.find(item => item.accountingCode === accountingCode);
        if (item) {
            // 找到所屬分類
            const category = categories.find(cat => cat.id == item.categoryId && cat.type === transactionType);
            if (category) {
                return `${category.name}/${item.accountingName}(${item.accountingCode})`;
            } else {
                return `${item.accountingName}(${item.accountingCode})`;
            }
        } else {
            return accountingCode;
        }
    } catch (error) {
        console.error('處理交易描述時發生錯誤:', error);
        return accountingCode;
    }
}

// 取得交易類型文字
function getTransactionTypeText(transactionType,entityId) {
    switch (transactionType) {
        case 'income':
            return '收入';
        case 'expense':
            return '支出';
        case 'transfer':
            if(entityId === currentAccountId){
                return '轉移-轉入';
            }else{
                return '轉移-轉出';
            }
        default:
            return '未知';
    }
}
// 取得狀態文字
function getStatusText(status) {
    switch (status) {
        case 'completed':
            return '已完成';
        case 'pending':
            return '待處理';
        case 'cancelled':
            return '已取消';
        default:
            return '未知';
    }
}

//--------------------------------UI更新--------------------------------

// 更新顯示
async function updateDisplay() {
    const tbody = document.getElementById('transactionTableBody');
    tbody.innerHTML = '';

    if(!currentAccountId){
        tbody.innerHTML = `
        <tr>
            <td colspan="7" class="p-6 text-2xl text-center text-gray-500">
                <span class="fa fa-exclamation-triangle"></span>
                尚未選擇帳戶
            </td>
        </tr>
        `;
        return;
    }else if(futureFilteredTransactions.length === 0){
        tbody.innerHTML = `
        <tr>
            <td colspan="7" class="p-6 text-2xl text-center text-gray-500">
                無交易記錄
            </td>
        </tr>
        `;
        return;
    }

    // 取得當前頁數資料
    const pageTransactions = dataPaging.getDataOfCurrentPage();
    
    // 更新表格資訊
    for (const transaction of pageTransactions) {
        //const transactionDescription = await getTransactionDescription(transaction.paymentDescription, transaction.transactionType);
        const transactionDescription = transaction.notes || await getTransactionDescription(transaction.paymentDescription, transaction.transactionType);

        // 移除未使用的變數
        const targetEntityInfo = await getTargetEntityInfo(transaction);
        const row = document.createElement('tr');
        const transactionTags = transaction.tags;

        // 處理日期顯示邏輯
        let displayDate = '';
        let dateNote = '';
        if (transaction.paymentDate) {
            displayDate = transaction.paymentDate;
        } else if (transaction.expectedPaymentDate) {
            displayDate = transaction.expectedPaymentDate;
            dateNote = ' (預計)';
        } else {
            displayDate = '未設定日期';
        }

        // 移除未使用的轉帳顯示邏輯變數
        
        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap">
                ${displayDate}
                ${dateNote ? `<span class="text-xs text-gray-500">${dateNote}</span>` : ''}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                    ${getTransactionTypeStyle(transaction.transactionType,transaction.entityId)}">
                    ${getTransactionTypeText(transaction.transactionType,transaction.entityId)}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">${transactionDescription}</td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                ${getEntityStyle(targetEntityInfo.type)}">
                 ${targetEntityInfo.type} - ${targetEntityInfo.name}
                 </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                    ${transactionTags.map(t=>{
                        const isSelected = selectedTags.includes(t);
                        const bgClass = isSelected ? 'bg-blue-100 text-blue-800 border border-blue-300' : 'bg-red-50 text-red-800 border border-red-200';
                        const hoverClass = isSelected ? 'hover:bg-blue-200' : 'hover:bg-red-100';
                        const title = isSelected ? '點擊取消過濾此標籤' : '點擊過濾此標籤';
                        return `<span class="px-2 ml-2 inline-flex text-xs leading-5 font-semibold rounded-full ${bgClass} ${hoverClass} cursor-pointer transition-all duration-200 transform hover:scale-105"
                                      data-tag-name="${t}"
                                      title="${title}"
                                      onclick="handleTagClick('${t}', event)">${t}</span>`;
                    }).join('')}
            </td>
            <td class="px-6 py-4 whitespace-nowrap 
                ${getAmountStyle(transaction.transactionType,transaction.entityId)}">
                ${getAmountText(transaction.transactionType,transaction.entityId,transaction.amount)}
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                    ${getStatusStyle(transaction.transactionStatus)}">
                    ${getStatusText(transaction.transactionStatus)}
                </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex space-x-2">
                    <button onclick="viewTransactionDetail('${transaction.id}')" 
                        class="text-blue-600 hover:text-blue-900">
                        查看
                    </button>
                </div>
            </td>
        `;
        
        tbody.appendChild(row);
    }
    
    // 更新分頁資訊
    updatePageInfo();
}



// 更新餘額顯示
function updateBalanceDisplay(currentBalance, futureBalance) {
    const balanceCardContent = document.querySelector('#balanceCard .flex.flex-col');

    if (!balanceCardContent) {
        console.error('找不到餘額卡片內容容器');
        return;
    }

    // 格式化金額的輔助函數
    const formatBalance = (balance) => {
        const formattedBalance = new Intl.NumberFormat('zh-TW', {
            style: 'currency',
            currency: 'TWD',
            minimumFractionDigits: 0
        }).format(Math.abs(balance));
        return `${balance >= 0 ? '' : '-'}${formattedBalance}`;
    };

    const currentFormatted = formatBalance(currentBalance);
    const futureFormatted = formatBalance(futureBalance);

    // 判斷是否需要顯示兩個餘額
    const showBothBalances = currentBalance !== futureBalance;

    if (showBothBalances) {
        // 顯示對比樣式
        balanceCardContent.innerHTML = `
            <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
                <!-- 當前餘額 -->
                <div class="w-full sm:flex-1 bg-gray-50 rounded-lg p-6 text-center">
                    <div class="text-2xl sm:text-3xl font-bold mb-2 ${currentBalance >= 0 ? 'text-green-600' : 'text-red-600'}">${currentFormatted}</div>
                    <div class="text-blue-600 font-medium">當前收支結餘</div>
                </div>

                <!-- 箭頭和說明 -->
                <div class="flex-shrink-0 text-center">
                    <div class="text-2xl sm:text-3xl text-gray-400 mb-2 transform sm:rotate-0 rotate-90">→</div>
                    <div class="text-xs sm:text-sm text-gray-600 whitespace-nowrap">
                        <div>應收付款項</div>
                        <div>到帳後的收支結餘</div>
                    </div>
                </div>

                <!-- 未來預估餘額 -->
                <div class="w-full sm:flex-1 bg-gray-50 rounded-lg p-6 text-center">
                    <div class="text-2xl sm:text-3xl font-bold mb-2 ${futureBalance >= 0 ? 'text-green-600' : 'text-red-600'}">${futureFormatted}</div>
                    <div class="text-yellow-600 font-medium">未來預估餘額</div>
                </div>
            </div>
        `;
    } else {
        // 顯示單一餘額
        balanceCardContent.innerHTML = `
            <div class="text-center">
                <div class="inline-block bg-gray-50 rounded-lg p-8 min-w-64">
                    <div class="text-3xl sm:text-4xl font-bold mb-3 ${currentBalance >= 0 ? 'text-green-600' : 'text-red-600'}">${currentFormatted}</div>
                    <div class="text-gray-600 font-medium">帳戶餘額</div>
                </div>
            </div>
        `;
    }
}

// updateFutureBalanceDisplay 函數已整合到 updateBalanceDisplay 中

// 更新過濾結果統計顯示
function updateFilteredBalanceDisplay(currentFilteredBalance, futureFilteredBalance, filters) {
    const filteredBalanceCard = document.getElementById('filteredBalanceCard');
    if (!filteredBalanceCard) return;

    // 檢查是否有任何過濾條件
    const hasFilters = (filters.type && filters.type !== '') ||
                      (filters.startDate && filters.startDate !== '') ||
                      (filters.endDate && filters.endDate !== '') ||
                      (filters.tags && filters.tags.length > 0);

    if (!hasFilters) {
        // 沒有過濾條件時隱藏統計卡片
        filteredBalanceCard.style.display = 'none';
        return;
    }

    // 顯示統計卡片
    filteredBalanceCard.style.display = 'block';

    // 格式化金額
    const formatBalance = (balance) => {
        const formattedBalance = new Intl.NumberFormat('zh-TW', {
            style: 'currency',
            currency: 'TWD',
            minimumFractionDigits: 0
        }).format(Math.abs(balance));
        return `${balance >= 0 ? '+' : '-'}${formattedBalance}`;
    };

    const currentFormatted = formatBalance(currentFilteredBalance);
    const futureFormatted = formatBalance(futureFilteredBalance);

    // 判斷是否需要顯示兩個金額
    const showBothAmounts = currentFilteredBalance !== futureFilteredBalance;

    let balanceContent = '';
    if (showBothAmounts) {
        // 顯示兩個不同的金額 - 對比樣式
        balanceContent = `
            <div class="flex flex-col sm:flex-row items-center justify-between mt-4 gap-4">
                <!-- 現況過濾金額 -->
                <div class="w-full sm:flex-1 bg-gray-50 rounded-lg p-4 text-center">
                    <div class="text-xl sm:text-2xl font-bold ${currentFilteredBalance >= 0 ? 'text-green-600' : 'text-red-600'} mb-1">${currentFormatted}</div>
                    <div class="text-purple-600 font-medium text-sm">現況過濾金額</div>
                </div>

                <!-- 箭頭 -->
                <div class="flex-shrink-0 text-center">
                    <div class="text-xl sm:text-2xl text-gray-400 transform sm:rotate-0 rotate-90">→</div>
                </div>

                <!-- 未來過濾金額 -->
                <div class="w-full sm:flex-1 bg-gray-50 rounded-lg p-4 text-center">
                    <div class="text-xl sm:text-2xl font-bold ${futureFilteredBalance >= 0 ? 'text-green-600' : 'text-red-600'} mb-1">${futureFormatted}</div>
                    <div class="text-purple-600 font-medium text-sm">未來過濾金額</div>
                </div>
            </div>
        `;
    } else {
        // 顯示單一金額 - 居中樣式
        balanceContent = `
            <div class="mt-4 text-center">
                <div class="inline-block bg-gray-50 rounded-lg p-6 min-w-48">
                    <div class="text-2xl sm:text-3xl font-bold ${currentFilteredBalance >= 0 ? 'text-green-600' : 'text-red-600'} mb-2">${currentFormatted}</div>
                    <div class="text-purple-600 font-medium">過濾結果金額</div>
                </div>
            </div>
        `;
    }

    // 生成過濾條件描述
    const filterDescriptions = [];
    if (filters.type) {
        const typeText = filters.type === 'income' ? '收入' : filters.type === 'expense' ? '支出' : '轉移';
        filterDescriptions.push(`類型：${typeText}`);
    }
    if (filters.startDate) filterDescriptions.push(`起始：${filters.startDate}`);
    if (filters.endDate) filterDescriptions.push(`結束：${filters.endDate}`);
    if (filters.tags && filters.tags.length > 0) {
        filterDescriptions.push(`標籤：${filters.tags.join(', ')}`);
    }

    const filterText = filterDescriptions.join(' | ');

    // 更新卡片內容
    document.getElementById('filteredBalanceContent').innerHTML = `
        <div>
            <div class="text-center mb-2">
                <h3 class="text-lg font-bold text-purple-800">過濾結果統計</h3>
                <p class="text-sm text-purple-600 mt-1">${filterText}</p>
            </div>
            ${balanceContent}
        </div>
    `;
}



//--------------------------------交易明細事件處理--------------------------------

// 查看交易明細
function viewTransactionDetail(id) {
    viewCompleteDetail(id);
}


// 查看完整資訊
async function viewCompleteDetail(id) {
    const transaction = futureFilteredTransactions.find(t => t.id === id);

    if (!transaction) return;

    try {
        const transactionDescription = await getTransactionDescription(transaction.paymentDescription, transaction.transactionType);
        const targetEntityInfo = await getTargetEntityInfo(transaction);
        // 獲取明細資料.
        const details = await getTransactionDetailByTransactionId(transaction.id);

        // 構建顯示內容
        let content = `
            <div class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div class="col-span-2">
                        <div class="mt-1"><span class="p-2 inline-flex text-sm leading-5 font-semibold rounded-full 
                            ${getStatusStyle(transaction.transactionStatus)}">
                            ${getStatusText(transaction.transactionStatus)}
                        </span></div>
                    </div>
                    <div class="grid grid-cols-1 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">交易日期</label>
                            <div class="m-2 text-sm text-gray-900">${transaction.paymentDate || "無紀錄"}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">預計收款日期</label>
                            <div class="m-2 text-sm text-gray-900">${transaction.expectedPaymentDate||"無紀錄"}</div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">憑證/發票日期</label>
                            <div class="m-2 text-sm text-gray-900">${transaction.invoiceDate||"無紀錄"}</div>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">交易類型</label>
                        <div class="mt-1 text-sm text-gray-900"><span class="p-2 inline-flex text-sm leading-5 font-semibold rounded-full 
                            ${getTransactionTypeStyle(transaction.transactionType,transaction.entityId)}">
                            ${getTransactionTypeText(transaction.transactionType,transaction.entityId)}
                        </span></div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">帳戶</label>
                        <div class="mt-1 text-sm text-gray-900">
                            <span class="p-2 inline-flex text-sm leading-5 font-semibold rounded-full bg-gray-200 text-blue-900">
                            ${await getAccountName(transaction.accountId)}
                            </span>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">交易對象</label>
                        <div class="mt-1 text-sm text-gray-900"><span class="p-2 inline-flex text-sm leading-5 font-semibold rounded-full 
                            ${getEntityStyle(targetEntityInfo.type)}">
                            ${targetEntityInfo.type} - ${targetEntityInfo.name}
                        </span></div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">會計科目</label>
                        <div class="m-2 text-sm text-gray-900">${transactionDescription}</div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">金額</label>
                        <div class="m-2 text-sm text-gray-900">${formatCurrency(transaction.amount)}</div>
                    </div>                            
                    <div class="col-span-2">
                        <label class="block text-sm font-medium text-gray-700">說明</label>
                        <div class="m-2 text-sm text-gray-900 whitespace-pre-wrap">${transaction.notes || '無'}</div>
                    </div>
                </div>`;

        // 如果有明細資料，添加明細表格
        if (details && details.length > 0) {
            content += `
                <div class="mt-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-4">明細項目</h4>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">項次</th>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">項目</th>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">單價</th>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">數量</th>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">單位</th>
                                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">小計</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">`;

            details.forEach(detail => {
                content += `
                    <tr>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">${detail.itemNo}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">${detail.item}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">${detail.price}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">${detail.quantity}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">${detail.unit}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">${detail.total}</td>
                    </tr>`;
            });

            content += `
                            </tbody>
                        </table>
                    </div>
                </div>`;
        }

        content += `</div>`;

        // 顯示模態框
        showModal('查看交易明細', content);
    } catch (error) {
        console.error('載入交易明細失敗：', error);
        alert('載入交易明細失敗');
    }
}

 // showModal 函數已移至 ModalUtils.js 統一管理
 // 現在使用全域的 showModal 函數，由 ModalUtils.js 提供
 // 顯示模態框
 /*
 function showModal(title, content) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 z-50 overflow-y-auto';
    modal.innerHTML = `
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" aria-hidden="true">
                <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">${title}</h3>
                            ${content}
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="button" onclick="this.closest('.fixed').remove()" 
                            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:w-auto sm:text-sm">
                        關閉
                    </button>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
}
*/

//--------------------------------自訂樣式及格式化--------------------------------

// 格式化金額
// formatCurrency 函數已移至 CommonUtils.js 統一管理
function formatCurrency(amount) {
    return CommonUtils.formatCurrency(amount, { absoluteValue: true });
}



//取得金額樣式
function getAmountStyle(transactionType,entityId) {
    if(transactionType === 'income' || 
        (transactionType === 'transfer' && entityId === currentAccountId)){
        return 'text-green-600';
    }else{
        return 'text-red-600';
    }
}
//取得金額文字
function getAmountText(transactionType,entityId,amount) {
    // 格式化金額
    const formattedAmount = new Intl.NumberFormat('zh-TW', {
            style: 'currency',
            currency: 'TWD',
            minimumFractionDigits: 0
        }).format(Math.abs(amount));              
    if(transactionType === 'income' || 
        (transactionType === 'transfer' && entityId === currentAccountId)){
        return '+'+formattedAmount;
    }else{
        return '-'+formattedAmount;
    }
}
// 取得交易類型樣式
function getTransactionTypeStyle(transactionType,entityId) {
    switch (transactionType) {
        case 'income':
            return 'bg-green-100 text-green-800';
        case 'expense':
            return 'bg-red-100 text-red-800';
        case 'transfer':
            if(entityId === currentAccountId){
                return 'bg-gray-100 text-green-800';
            }else{
                return 'bg-gray-100 text-red-800';
            }
        default:
            return 'bg-gray-100 text-gray-800';
    }
}

// 取得狀態樣式
function getStatusStyle(status) {
    switch (status) {
        case 'completed':
            return 'bg-green-100 text-green-800';
        case 'pending':
            return 'bg-yellow-100 text-yellow-800';
        case 'cancelled':
            return 'bg-red-100 text-red-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
}
// 取得狀態樣式
function getEntityStyle(entityType) {
    switch (entityType) {
        case '帳戶':
            return 'bg-green-100 text-green-800';
        case '員工':
            return 'bg-yellow-100 text-yellow-800';
        case '客戶':
            return 'bg-blue-100 text-blue-800';
        case '供應商':
            return 'bg-purple-100 text-purple-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
}