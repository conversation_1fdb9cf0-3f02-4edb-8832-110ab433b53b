<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>帳戶交易明細 - 益芯能源工程-管理系統</title>
    <link rel="icon" href="../../common/img/logo.png" type="image/x-icon">

    <!-- 模組導入映射設定 -->
    <script type="importmap">
    {
        "imports": {
        "@common/": "/common/"
        }
    }
    </script>

    <!-- 樣式庫載入 -->
    <script src="../../common/lib/tailwindcss3.4.16.js"></script> <!-- Tailwind CSS 框架 -->
    <link href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.0.0/css/all.min.css" rel="stylesheet"> <!-- Font Awesome 圖示庫 -->

    <!-- Firebase 相關庫載入 -->
    <script src="https://www.gstatic.com/firebasejs/11.7.3/firebase-app-compat.js"></script> <!-- Firebase 核心 -->
    <script src="https://www.gstatic.com/firebasejs/11.7.3/firebase-analytics-compat.js"></script> <!-- Firebase 分析 -->
    <script src="https://www.gstatic.com/firebasejs/11.7.3/firebase-firestore-compat.js"></script> <!-- Firebase 資料庫 -->
    <script src="https://www.gstatic.com/firebasejs/11.7.3/firebase-auth-compat.js"></script> <!-- Firebase 認證 -->

    <!-- 自定義工具庫載入 -->
    <script src="../../common/firebaseAPI/auth.js"></script> <!-- 認證相關功能 -->
    <script src="../../common/db/db.js"></script> <!-- 資料庫操作功能 -->
    <script src="../../common/db/preload.js"></script> <!-- 資料預載功能 -->
    <script src="../../common/utils/CommonUtils.js"></script> <!-- 通用工具函數 -->
    <script src="../../common/utils/pageTransfer.js"></script> <!-- 頁面轉換工具 -->
    <script src="../../common/utils/dataPaging.js"></script> <!-- 資料分頁工具函數 -->
    <script src="../../common/utils/ModalUtils.js"></script> <!-- 模態框工具 -->
    <script src="../../common/utils/DatabaseErrors.js"></script> <!-- 資料庫錯誤處理 -->

    <!-- 自定義樣式 -->
    <style>
        /* 導航選單懸停效果 */
        .nav-item:hover>.submenu {
            display: block; /* 懸停時顯示子選單 */
        }

        /* 子選單基本樣式 */
        .submenu {
            display: none; /* 預設隱藏 */
            position: absolute; /* 絕對定位 */
            background-color: white; /* 白色背景 */
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); /* 陰影效果 */
            z-index: 1000; /* 層級設定 */
            top: 100%; /* 位於父元素下方 */
            left: 0; /* 左對齊 */
        }

        /* 多層子選單定位 */
        .submenu .submenu {
            top: 0; /* 與父選單同高 */
            left: 100%; /* 位於父選單右側 */
        }

        /* 導航項目相對定位 */
        .nav-item {
            position: relative; /* 相對定位以支援子選單 */
        }
    </style>

    <!-- 導航功能載入 -->
    <script src="../../common/navigation/navigation.js"></script>
</head>
<body class="flex flex-col min-h-screen bg-gray-100">
    <main class="container mx-auto px-4 py-8">
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-800">帳戶交易明細</h1>
            <p class="text-gray-600">查看特定帳戶的交易記錄</p>
        </div>

        <!-- 帳戶選擇 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-bold text-gray-800 mb-6">選擇帳戶</h2>
            <div class="max-w-md">
                <select id="accountSelect" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500" onchange="handleAccountChange()">
                    <option value="">請選擇帳戶</option>
                </select>
            </div>
        </div>

        <!-- 帳戶餘額卡片 -->
        <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
            <!-- 帳戶餘額對比卡片 -->
            <div id="balanceCard" class="bg-white rounded-lg shadow-md p-6 mb-8 w-full hidden">
                <h2 class="text-xl font-bold text-gray-800 mb-6">帳戶餘額</h2>

                <div class="flex flex-col sm:flex-row items-center justify-center gap-4">
                    <!-- 內容將由 JavaScript 動態填充 -->
                    <div class="text-center">
                        <div class="text-gray-500">載入中...</div>
                    </div>
                </div>
            </div>

            <!-- 過濾結果統計 -->
            <div id="filteredBalanceCard" class="border-2 border-purple-500 bg-purple-50 rounded-lg shadow-md p-6 mb-8" style="display: none;">
                <div id="filteredBalanceContent">
                    <!-- 內容將由 JavaScript 動態填充 -->
                </div>
            </div>
        </div>

        <!-- 篩選器 -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <form id="filterForm" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700">交易類型</label>
                    <select id="typeFilter" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        <option value="">全部</option>
                        <option value="income">收入</option>
                        <option value="expense">支出</option>
                        <option value="transfer">轉移</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">起始日期</label>
                    <input type="date" id="startDate" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">結束日期</label>
                    <input type="date" id="endDate" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                </div>
                <div class="flex items-end">
                    <button type="submit" class="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                        搜尋
                    </button>
                </div>
            </form>

            <!-- 標籤搜尋框 -->
            <div class="mt-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-search mr-1"></i>
                    標籤搜尋
                </label>
                <div class="relative">
                    <input
                        type="text"
                        id="tagSearchInput"
                        placeholder="輸入標籤名稱進行搜尋..."
                        class="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500 focus:ring-1"
                        autocomplete="off"
                    >
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <div id="tagSearchDropdown" class="absolute z-20 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg hidden max-h-60 overflow-y-auto">
                        <!-- 搜尋結果將由 JavaScript 動態填充 -->
                    </div>
                </div>
            </div>

            <!-- 已選擇標籤顯示區域 -->
            <div id="selectedTagsContainer" class="mt-4 p-3 bg-gray-50 rounded-lg" style="display: none;">
                <!-- 標籤內容將由 JavaScript 動態填充 -->
            </div>
        </div>

        <!-- 交易記錄列表 -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-xl font-bold text-gray-800">交易記錄</h2>
                <div class="text-sm text-gray-500">
                    <i class="fas fa-info-circle mr-1"></i>
                    點擊標籤可進行過濾
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">交易日期</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">交易類型</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">描述</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">對象</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">標籤</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">金額</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">狀態</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                        </tr>
                    </thead>
                    <tbody id="transactionTableBody" class="bg-white divide-y divide-gray-200">
                        <!-- 資料將由 JavaScript 動態填充 -->
                    </tbody>
                </table>
            </div>
            <!-- 分頁控制 -->
            <div class="mt-6">
                <!-- 分頁資訊 -->
                <div class="flex items-center justify-between mb-4">
                    <div class="text-sm text-gray-700">
                        顯示第 <span id="pageStartItem" class="font-medium">1</span> 到
                        <span id="pageEndItem" class="font-medium">10</span> 項，
                        共 <span id="totalItems" class="font-medium">0</span> 項結果
                    </div>
                    <div class="text-sm text-gray-700">
                        第 <span id="currentPageNum" class="font-medium">1</span> 頁，
                        共 <span id="totalPages" class="font-medium">1</span> 頁
                    </div>
                </div>

                <!-- 分頁按鈕 -->
                <div class="flex items-center justify-center space-x-1">
                    <!-- 第一頁 -->
                    <button id="firstPage" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed" title="第一頁">
                        <i class="fas fa-angle-double-left"></i>
                    </button>

                    <!-- 上一頁 -->
                    <button id="prevPage" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed" title="上一頁">
                        <i class="fas fa-angle-left"></i>
                    </button>

                    <!-- 頁碼按鈕容器 -->
                    <div id="pageNumbersContainer" class="flex items-center">
                        <!-- 頁碼按鈕將由 JavaScript 動態生成 -->
                    </div>

                    <!-- 下一頁 -->
                    <button id="nextPage" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border-t border-b border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed" title="下一頁">
                        <i class="fas fa-angle-right"></i>
                    </button>

                    <!-- 最後一頁 -->
                    <button id="lastPage" class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed" title="最後一頁">
                        <i class="fas fa-angle-double-right"></i>
                    </button>
                </div>

                <!-- 快速跳轉 -->
                <div class="flex items-center justify-center mt-4 space-x-2">
                    <span class="text-sm text-gray-700">跳轉至</span>
                    <input type="number" id="jumpToPage" min="1" class="w-16 px-2 py-1 text-sm border border-gray-300 rounded focus:border-blue-500 focus:ring-1 focus:ring-blue-500" placeholder="頁">
                    <button id="jumpButton" class="px-3 py-1 text-sm font-medium text-white bg-blue-600 border border-transparent rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        跳轉
                    </button>
                </div>
            </div>
        </div>
    </main>

    <footer class="bg-white shadow-lg mt-auto">
        <div class="container mx-auto px-4 py-6">
            <p class="text-center text-gray-600">© 2024 益芯能源工程-管理系統. All rights reserved.</p>
        </div>
    </footer>
    <script src="accountTransactionDetails.js"></script>
    <script src="accountTransactionDetails_Service.js"></script>
</body>
</html> 