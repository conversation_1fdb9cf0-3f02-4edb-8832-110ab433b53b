# 標籤搜尋功能實作說明

## 📋 功能概述

新增了標籤搜尋框功能，支援模糊搜尋並可以點擊選擇標籤。已選擇的標籤不會顯示在搜尋結果中，避免重複選擇。

## ✨ 主要功能

### 1. **智能搜尋框**
- **模糊搜尋**：支援部分關鍵字匹配
- **即時搜尋**：輸入時即時顯示結果
- **高亮顯示**：搜尋關鍵字在結果中高亮顯示
- **去重顯示**：已選擇的標籤不會出現在搜尋結果中

### 2. **鍵盤導航**
- **上下箭頭**：瀏覽搜尋結果
- **Enter 鍵**：選擇當前高亮的標籤
- **Escape 鍵**：關閉搜尋下拉選單
- **自動滾動**：確保選中項目在可視範圍內

### 3. **用戶體驗優化**
- **視覺反饋**：懸停效果和選中高亮
- **圖示提示**：搜尋圖示和添加圖示
- **自動清空**：選擇標籤後自動清空搜尋框
- **點擊外部關閉**：點擊搜尋框外部自動關閉下拉選單

## 🎯 使用方式

### 基本操作
1. **開始搜尋**：在標籤搜尋框中輸入關鍵字
2. **查看結果**：系統會即時顯示匹配的標籤列表
3. **選擇標籤**：
   - 點擊想要的標籤
   - 或使用鍵盤上下箭頭選擇，按 Enter 確認
4. **自動應用**：選擇後標籤會自動添加到過濾條件中

### 鍵盤快捷鍵
- **↑/↓**：在搜尋結果中導航
- **Enter**：選擇當前高亮的標籤
- **Escape**：關閉搜尋下拉選單

## 🔧 技術實現

### 核心變數
```javascript
let allAvailableTags = []; // 存儲所有可用的標籤
```

### 主要函數
- `initializeTagSearch()` - 初始化搜尋功能
- `loadAllAvailableTags()` - 載入帳戶的所有標籤
- `handleTagSearch()` - 處理搜尋輸入
- `displayTagSearchResults()` - 顯示搜尋結果
- `selectTagFromSearch()` - 選擇標籤
- `handleTagSearchKeydown()` - 處理鍵盤導航

### 數據來源
標籤數據來自當前選定帳戶的所有交易記錄：
```javascript
// 從當前帳戶的所有交易中提取標籤
const accountTransactions = transactions.filter(t => 
    t.accountId === currentAccountId || 
    (t.entityId === currentAccountId && t.entityType === 'account')
);
```

## 🎨 UI 設計

### 搜尋框樣式
- **搜尋圖示**：左側顯示搜尋圖示
- **佔位符文字**：「輸入標籤名稱進行搜尋...」
- **焦點效果**：藍色邊框和陰影

### 下拉選單樣式
- **最大高度**：60 (15rem)，超出時顯示滾動條
- **懸停效果**：淺藍色背景
- **邊框分隔**：每個選項間有淺灰色分隔線
- **圖示提示**：右側顯示加號圖示

### 搜尋結果顯示
```html
<div class="px-4 py-3 hover:bg-blue-50 cursor-pointer">
    <div class="flex items-center justify-between">
        <span class="text-sm">標籤名稱（高亮關鍵字）</span>
        <i class="fas fa-plus text-gray-400"></i>
    </div>
</div>
```

## 🔄 整合性

### 與現有功能的整合
1. **標籤過濾**：搜尋選擇的標籤會自動添加到過濾條件
2. **帳戶切換**：切換帳戶時會重新載入該帳戶的標籤
3. **狀態同步**：與現有的標籤選擇狀態保持同步
4. **URL 參數**：選擇的標籤會保存到 URL 參數中

### 數據流程
```
用戶輸入 → 模糊搜尋 → 顯示結果 → 用戶選擇 → 添加到過濾條件 → 重新載入交易記錄
```

## 📊 搜尋邏輯

### 模糊搜尋算法
```javascript
const filteredTags = allAvailableTags.filter(tag => 
    tag.toLowerCase().includes(searchTerm.toLowerCase()) && 
    !selectedTags.includes(tag)
);
```

### 關鍵字高亮
```javascript
const highlightedTag = text.replace(
    new RegExp(`(${searchTerm})`, 'gi'), 
    '<mark class="bg-yellow-200">$1</mark>'
);
```

## 🚀 性能優化

### 載入策略
- **按需載入**：只在選擇帳戶時載入該帳戶的標籤
- **去重處理**：使用 Set 自動去除重複標籤
- **排序優化**：標籤按字母順序排列，提升查找效率

### 事件處理
- **防抖處理**：搜尋輸入使用即時響應，無需防抖
- **延遲關閉**：失去焦點時延遲 200ms 關閉，確保點擊事件能觸發
- **事件委託**：使用事件委託處理動態生成的搜尋結果

## 📝 注意事項

### 數據安全
- **HTML 清理**：選擇標籤時會清理 HTML 標記
- **輸入驗證**：確保標籤名稱不為空

### 用戶體驗
- **即時反饋**：搜尋結果即時更新
- **視覺一致性**：與現有 UI 風格保持一致
- **無障礙支援**：支援鍵盤導航

### 錯誤處理
- **空結果處理**：沒有匹配結果時顯示友好提示
- **異常捕獲**：載入標籤失敗時的錯誤處理
- **容錯機制**：DOM 元素不存在時的安全檢查

## 🔮 未來擴展

### 可能的改進
1. **搜尋歷史**：記住用戶的搜尋歷史
2. **標籤分類**：按類別組織標籤
3. **批量操作**：支援一次選擇多個標籤
4. **自定義標籤**：允許用戶創建新標籤
5. **搜尋建議**：基於使用頻率的智能建議

### 技術優化
1. **虛擬滾動**：大量標籤時的性能優化
2. **模糊匹配**：更智能的搜尋算法
3. **快取機制**：標籤數據的本地快取
4. **國際化**：多語言支援
