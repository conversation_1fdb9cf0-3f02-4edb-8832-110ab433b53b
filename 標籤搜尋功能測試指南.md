# 標籤搜尋功能測試指南

## 🧪 測試目標

驗證標籤搜尋框的各項功能是否正常工作，確保用戶體驗流暢。

## 📋 測試清單

### 1. 基本搜尋功能
- [ ] **搜尋框顯示**：確認搜尋框正確顯示，有搜尋圖示
- [ ] **輸入響應**：輸入文字時即時顯示搜尋結果
- [ ] **模糊搜尋**：部分關鍵字能匹配到相關標籤
- [ ] **大小寫不敏感**：大小寫不影響搜尋結果

### 2. 搜尋結果顯示
- [ ] **結果列表**：搜尋結果正確顯示在下拉選單中
- [ ] **關鍵字高亮**：搜尋關鍵字在結果中被高亮顯示
- [ ] **無結果提示**：沒有匹配結果時顯示友好提示
- [ ] **已選標籤過濾**：已選擇的標籤不出現在搜尋結果中

### 3. 鍵盤導航
- [ ] **上下箭頭**：可以使用上下箭頭鍵瀏覽結果
- [ ] **Enter 選擇**：按 Enter 鍵選擇當前高亮的標籤
- [ ] **Escape 關閉**：按 Escape 鍵關閉下拉選單
- [ ] **自動滾動**：選中項目自動滾動到可視範圍

### 4. 滑鼠操作
- [ ] **點擊選擇**：點擊標籤能正確選擇
- [ ] **懸停效果**：滑鼠懸停時有視覺反饋
- [ ] **點擊外部關閉**：點擊搜尋框外部關閉下拉選單

### 5. 整合功能
- [ ] **標籤添加**：選擇標籤後正確添加到過濾條件
- [ ] **搜尋框清空**：選擇標籤後搜尋框自動清空
- [ ] **過濾應用**：選擇標籤後交易記錄正確過濾
- [ ] **狀態同步**：與現有標籤選擇狀態保持同步

## 🎯 測試場景

### 場景 1：首次使用
**步驟**：
1. 選擇一個有交易記錄的帳戶
2. 在標籤搜尋框中輸入文字
3. 觀察搜尋結果

**預期結果**：
- 搜尋框正常顯示
- 輸入時即時顯示相關標籤
- 標籤按字母順序排列

### 場景 2：模糊搜尋測試
**步驟**：
1. 輸入標籤的部分文字（如：標籤「辦公用品」，輸入「辦公」）
2. 觀察搜尋結果

**預期結果**：
- 包含「辦公」的所有標籤都會顯示
- 關鍵字「辦公」被高亮顯示

### 場景 3：鍵盤導航測試
**步驟**：
1. 在搜尋框中輸入文字顯示結果
2. 使用上下箭頭鍵瀏覽
3. 按 Enter 鍵選擇

**預期結果**：
- 箭頭鍵能正確切換選中項目
- 選中項目有藍色背景高亮
- Enter 鍵能選擇當前高亮的標籤

### 場景 4：已選標籤過濾測試
**步驟**：
1. 先選擇一個標籤
2. 再次搜尋該標籤

**預期結果**：
- 已選擇的標籤不會出現在搜尋結果中
- 搜尋其他標籤正常顯示

### 場景 5：帳戶切換測試
**步驟**：
1. 在帳戶 A 中搜尋標籤
2. 切換到帳戶 B
3. 再次搜尋標籤

**預期結果**：
- 不同帳戶顯示不同的標籤列表
- 標籤列表根據帳戶的交易記錄動態更新

## 🐛 常見問題排查

### 問題 1：搜尋框不顯示
**可能原因**：
- HTML 結構問題
- CSS 樣式問題
- JavaScript 初始化失敗

**排查步驟**：
1. 檢查瀏覽器控制台是否有錯誤
2. 確認 HTML 元素 ID 正確
3. 檢查 CSS 類別是否正確載入

### 問題 2：搜尋無結果
**可能原因**：
- 沒有載入標籤數據
- 搜尋邏輯錯誤
- 帳戶沒有交易記錄

**排查步驟**：
1. 檢查 `allAvailableTags` 陣列是否有數據
2. 確認 `loadAllAvailableTags()` 函數正常執行
3. 檢查帳戶是否有交易記錄

### 問題 3：鍵盤導航失效
**可能原因**：
- 事件監聽器未正確綁定
- DOM 元素選擇器錯誤
- 事件處理邏輯錯誤

**排查步驟**：
1. 檢查 `keydown` 事件是否正確綁定
2. 確認 `handleTagSearchKeydown` 函數正常執行
3. 檢查 DOM 查詢是否返回正確元素

### 問題 4：選擇標籤後無反應
**可能原因**：
- `selectTagFromSearch` 函數錯誤
- 標籤過濾邏輯問題
- 狀態更新失敗

**排查步驟**：
1. 檢查 `selectedTags` 陣列是否正確更新
2. 確認 `handleFilter` 函數被調用
3. 檢查交易記錄是否重新載入

## 📊 測試數據準備

### 建議的測試標籤
```
辦公用品
交通費
餐飲費
設備採購
軟體授權
會議費用
培訓費
廣告費
水電費
租金
```

### 測試用例
1. **完全匹配**：輸入「辦公用品」
2. **部分匹配**：輸入「辦公」
3. **大小寫測試**：輸入「辦公用品」、「辦公用品」
4. **特殊字符**：輸入包含特殊字符的標籤
5. **空輸入**：清空搜尋框
6. **不存在標籤**：輸入不存在的標籤名稱

## ✅ 驗收標準

### 功能完整性
- 所有基本功能正常工作
- 鍵盤和滑鼠操作都支援
- 與現有功能無衝突

### 性能表現
- 搜尋響應時間 < 100ms
- 大量標籤時滾動流暢
- 記憶體使用合理

### 用戶體驗
- 操作直觀易懂
- 視覺反饋及時
- 錯誤處理友好

### 兼容性
- 主流瀏覽器支援
- 響應式設計適配
- 無障礙功能支援

## 📝 測試報告模板

```
測試日期：____
測試環境：____
瀏覽器版本：____

基本搜尋功能：✅/❌
搜尋結果顯示：✅/❌
鍵盤導航：✅/❌
滑鼠操作：✅/❌
整合功能：✅/❌

發現問題：
1. ____
2. ____

建議改進：
1. ____
2. ____

總體評價：____
```
