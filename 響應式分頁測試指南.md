# 響應式分頁測試指南

## 🧪 測試目標

驗證分頁功能在不同螢幕尺寸下的響應式行為，確保用戶體驗在各種設備上都保持最佳狀態。

## 📋 測試清單

### 1. 螢幕尺寸響應測試
- [ ] **手機版 (< 480px)**：頁碼數量限制為3個
- [ ] **平板版 (480px - 768px)**：頁碼數量限制為5個
- [ ] **桌面版 (> 768px)**：頁碼數量限制為7個
- [ ] **窗口調整**：動態調整窗口大小時頁碼數量正確變化

### 2. 佈局響應測試
- [ ] **分頁資訊**：小螢幕垂直排列，大螢幕水平排列
- [ ] **快速跳轉**：小螢幕垂直排列，大螢幕水平排列
- [ ] **按鈕間距**：小螢幕較緊密，大螢幕較寬鬆
- [ ] **字體大小**：小螢幕較小字體，大螢幕正常字體

### 3. 功能完整性測試
- [ ] **所有按鈕可點擊**：各尺寸下所有按鈕都能正常點擊
- [ ] **頁碼跳轉**：頁碼按鈕在各尺寸下都能正確跳轉
- [ ] **快速跳轉**：輸入框和跳轉按鈕在各尺寸下正常工作
- [ ] **狀態保持**：響應式變化不影響當前頁狀態

### 4. 性能測試
- [ ] **防抖效果**：快速調整窗口大小時不會頻繁重新渲染
- [ ] **渲染流暢**：頁碼重新生成時無明顯卡頓
- [ ] **記憶體使用**：長時間使用無記憶體洩漏

## 🎯 測試場景

### 場景 1：手機版測試 (< 480px)
**測試步驟**：
1. 將瀏覽器窗口調整到 400px 寬度
2. 選擇有多頁數據的帳戶（至少10頁）
3. 觀察頁碼顯示數量
4. 測試各種頁面跳轉功能

**預期結果**：
- 最多顯示3個頁碼按鈕
- 分頁資訊垂直排列
- 快速跳轉區域垂直排列
- 按鈕使用較小的 padding 和字體

**頁碼顯示範例**：
```
當前頁 = 1：[1] [2] [3]
當前頁 = 5：[1] [...] [5] [...] [10]
當前頁 = 10：[8] [9] [10]
```

### 場景 2：平板版測試 (480px - 768px)
**測試步驟**：
1. 將瀏覽器窗口調整到 600px 寬度
2. 選擇有多頁數據的帳戶（至少10頁）
3. 觀察頁碼顯示數量
4. 測試各種頁面跳轉功能

**預期結果**：
- 最多顯示5個頁碼按鈕
- 分頁資訊開始水平排列
- 快速跳轉區域水平排列
- 按鈕大小適中

**頁碼顯示範例**：
```
當前頁 = 1：[1] [2] [3] [4] [5]
當前頁 = 5：[1] [...] [4] [5] [6] [...] [10]
當前頁 = 10：[6] [7] [8] [9] [10]
```

### 場景 3：桌面版測試 (> 768px)
**測試步驟**：
1. 將瀏覽器窗口調整到 1024px 寬度
2. 選擇有多頁數據的帳戶（至少15頁）
3. 觀察頁碼顯示數量
4. 測試各種頁面跳轉功能

**預期結果**：
- 最多顯示7個頁碼按鈕
- 分頁資訊水平排列，兩端對齊
- 快速跳轉區域水平排列
- 按鈕使用正常大小

**頁碼顯示範例**：
```
當前頁 = 1：[1] [2] [3] [4] [5] [6] [7] [...] [15]
當前頁 = 8：[1] [...] [5] [6] [7] [8] [9] [10] [11] [...] [15]
當前頁 = 15：[1] [...] [9] [10] [11] [12] [13] [14] [15]
```

### 場景 4：動態調整測試
**測試步驟**：
1. 從桌面版尺寸開始（1024px）
2. 逐漸縮小窗口到手機版尺寸（400px）
3. 再逐漸放大回桌面版尺寸
4. 觀察頁碼數量的變化

**預期結果**：
- 頁碼數量在斷點處正確變化（7→5→3→5→7）
- 變化過程流暢，無閃爍
- 當前頁狀態保持不變
- 防抖機制正常工作

### 場景 5：設備旋轉測試
**測試步驟**：
1. 在手機或平板上打開頁面
2. 在直向和橫向間切換
3. 觀察分頁顯示的變化

**預期結果**：
- 旋轉後頁碼數量根據新的螢幕寬度調整
- 佈局正確適應新的方向
- 功能保持完整

## 🔧 測試工具

### 瀏覽器開發者工具
1. **Chrome DevTools**：
   - 按 F12 打開開發者工具
   - 點擊設備模擬按鈕（手機圖示）
   - 選擇不同的設備預設或自定義尺寸

2. **Firefox Developer Tools**：
   - 按 F12 打開開發者工具
   - 點擊響應式設計模式按鈕
   - 調整視窗大小測試

### 實際設備測試
1. **手機測試**：iPhone、Android 手機
2. **平板測試**：iPad、Android 平板
3. **桌面測試**：不同解析度的顯示器

## 🐛 常見問題排查

### 問題 1：頁碼數量不正確
**可能原因**：
- `getMaxPageNumbers()` 函數邏輯錯誤
- 螢幕寬度檢測不準確
- CSS 斷點與 JavaScript 不一致

**排查步驟**：
1. 檢查 `window.innerWidth` 的值
2. 確認斷點設置是否正確
3. 檢查 `generatePageNumbers()` 函數執行

### 問題 2：佈局不響應
**可能原因**：
- CSS 類別未正確應用
- Tailwind CSS 未載入
- 響應式類別衝突

**排查步驟**：
1. 檢查元素的實際 CSS 類別
2. 確認 Tailwind CSS 正確載入
3. 檢查是否有 CSS 覆蓋

### 問題 3：窗口調整時不更新
**可能原因**：
- resize 事件監聽器未綁定
- 防抖函數問題
- 條件判斷錯誤

**排查步驟**：
1. 檢查 resize 事件是否正確綁定
2. 確認 `debounce` 函數正常工作
3. 檢查 `dataPaging` 對象狀態

### 問題 4：性能問題
**可能原因**：
- 防抖時間設置不當
- 頻繁的 DOM 操作
- 記憶體洩漏

**排查步驟**：
1. 調整防抖時間（建議 250ms）
2. 檢查是否有不必要的重新渲染
3. 使用瀏覽器性能工具分析

## 📊 測試數據記錄

### 測試記錄表格
| 螢幕寬度 | 預期頁碼數 | 實際頁碼數 | 佈局正確 | 功能正常 | 備註 |
|---------|-----------|-----------|---------|---------|------|
| 400px   | 3         |           |         |         |      |
| 600px   | 5         |           |         |         |      |
| 1024px  | 7         |           |         |         |      |

### 性能測試記錄
| 操作 | 響應時間 | CPU 使用 | 記憶體使用 | 備註 |
|------|---------|---------|-----------|------|
| 窗口調整 |         |         |           |      |
| 頁碼生成 |         |         |           |      |
| 頁面跳轉 |         |         |           |      |

## ✅ 驗收標準

### 功能完整性
- 所有螢幕尺寸下分頁功能完整
- 響應式變化不影響核心功能
- 頁碼數量根據螢幕大小正確調整

### 用戶體驗
- 佈局在各尺寸下都美觀實用
- 按鈕大小適合觸控操作
- 文字清晰易讀

### 性能表現
- 窗口調整時響應流暢
- 無明顯的性能瓶頸
- 記憶體使用穩定

### 兼容性
- 主流瀏覽器支援
- 各種設備正常工作
- 與現有功能無衝突

## 📝 測試報告模板

```
測試日期：____
測試環境：____
瀏覽器版本：____

螢幕尺寸響應：✅/❌
佈局響應：✅/❌
功能完整性：✅/❌
性能表現：✅/❌

發現問題：
1. ____
2. ____

性能數據：
- 平均響應時間：____
- 記憶體使用：____

建議改進：
1. ____
2. ____

總體評價：____
```
